@echo off
echo ===== OpenCV CPU 演示程序 =====
echo.

REM 检查xmake是否安装
where xmake >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到xmake。请先安装xmake: https://xmake.io/
    pause
    exit /b 1
)

echo 正在配置项目...
xmake f
if %ERRORLEVEL% neq 0 (
    echo 配置失败！
    pause
    exit /b 1
)

echo.
echo 正在构建项目...
xmake
if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 是否需要创建示例图像？(Y/N)
set /p CREATE_SAMPLE=

if /i "%CREATE_SAMPLE%"=="Y" (
    echo 正在创建示例图像...
    xmake run create_sample_image
    if %ERRORLEVEL% neq 0 (
        echo 创建示例图像失败！
        pause
        exit /b 1
    )
)

echo.
echo 是否要运行演示程序？(Y/N)
set /p RUN_DEMO=

if /i "%RUN_DEMO%"=="Y" (
    echo.
    echo 请输入图像路径(留空使用默认的sample.jpg):
    set /p IMAGE_PATH=

    if "%IMAGE_PATH%"=="" (
        echo 正在运行演示程序(使用默认图像)...
        xmake run image_processor
    ) else (
        echo 正在运行演示程序(使用指定图像)...
        xmake run image_processor "%IMAGE_PATH%"
    )
)

echo.
echo 完成！
pause
