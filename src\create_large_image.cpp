#include <opencv2/opencv.hpp>
#include <iostream>
#include <windows.h>

int main() {
    // 设置控制台输出为 UTF-8
    SetConsoleOutputCP(65001);
    
    std::cout << "=== 创建大尺寸测试图像 ===" << std::endl;
    
    // 创建一个4K分辨率的测试图像
    int width = 3840;
    int height = 2160;
    cv::Mat large_image(height, width, CV_8UC3, cv::Scalar(50, 50, 50));
    
    std::cout << "创建 " << width << "x" << height << " 的测试图像..." << std::endl;
    
    // 添加随机种子
    srand(time(nullptr));
    
    // 绘制大量圆形
    for (int i = 0; i < 500; i++) {
        cv::Point center(rand() % width, rand() % height);
        int radius = rand() % 100 + 20;
        cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
        cv::circle(large_image, center, radius, color, -1);
    }
    
    // 绘制大量矩形
    for (int i = 0; i < 300; i++) {
        cv::Point pt1(rand() % width, rand() % height);
        cv::Point pt2(rand() % width, rand() % height);
        cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
        cv::rectangle(large_image, pt1, pt2, color, -1);
    }
    
    // 绘制一些线条
    for (int i = 0; i < 200; i++) {
        cv::Point pt1(rand() % width, rand() % height);
        cv::Point pt2(rand() % width, rand() % height);
        cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
        cv::line(large_image, pt1, pt2, color, rand() % 10 + 1);
    }
    
    // 添加一些文本
    for (int i = 0; i < 50; i++) {
        cv::Point pos(rand() % (width-200), rand() % height);
        cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
        cv::putText(large_image, "CUDA Test " + std::to_string(i), pos, 
                   cv::FONT_HERSHEY_SIMPLEX, 1.0 + (rand() % 20) / 10.0, color, 2);
    }
    
    // 保存图像
    std::cout << "保存图像到 large_test.jpg..." << std::endl;
    bool success = cv::imwrite("large_test.jpg", large_image);
    
    if (success) {
        std::cout << "✓ 大尺寸测试图像创建成功: large_test.jpg" << std::endl;
        std::cout << "图像尺寸: " << width << "x" << height << std::endl;
        std::cout << "文件大小约: " << (width * height * 3) / (1024 * 1024) << " MB (未压缩)" << std::endl;
    } else {
        std::cerr << "✗ 创建图像失败!" << std::endl;
        return -1;
    }
    
    return 0;
}
