#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/cudaimgproc.hpp>
#include <opencv2/cudaarithm.hpp>
#include <opencv2/cudawarping.hpp>
#include <opencv2/tracking.hpp>
#include <iostream>
#include <chrono>
#include <vector>
#include <algorithm>
#include <windows.h>

class Timer {
public:
    Timer() : start_time(std::chrono::high_resolution_clock::now()) {}
    
    double elapsed() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        return duration.count() / 1000.0;
    }
    
    void reset() {
        start_time = std::chrono::high_resolution_clock::now();
    }

private:
    std::chrono::high_resolution_clock::time_point start_time;
};

class CVTasksDemo {
private:
    cv::dnn::Net detection_net;
    cv::HOGDescriptor hog_detector;
    cv::CascadeClassifier face_cascade;
    std::vector<cv::Ptr<cv::Tracker>> trackers;
    bool gpu_available;
    
public:
    CVTasksDemo() {
        // 检查GPU可用性
        gpu_available = (cv::cuda::getCudaEnabledDeviceCount() > 0);
        
        // 初始化HOG检测器
        hog_detector.setSVMDetector(cv::HOGDescriptor::getDefaultPeopleDetector());
        
        std::cout << "GPU可用: " << (gpu_available ? "是" : "否") << std::endl;
    }
    
    // 目标检测演示
    void demonstrateObjectDetection(const cv::Mat& image) {
        std::cout << "\n=== 目标检测演示 ===" << std::endl;
        Timer timer;
        
        // CPU版本 - HOG行人检测
        timer.reset();
        std::vector<cv::Rect> cpu_detections;
        hog_detector.detectMultiScale(image, cpu_detections, 1.05, cv::Size(8,8), cv::Size(32,32), 1.05, 2);
        double cpu_time = timer.elapsed();
        
        std::cout << "CPU HOG检测:" << std::endl;
        std::cout << "  检测时间: " << cpu_time << " ms" << std::endl;
        std::cout << "  检测数量: " << cpu_detections.size() << std::endl;
        
        // 绘制CPU检测结果
        cv::Mat cpu_result = image.clone();
        for (const auto& rect : cpu_detections) {
            cv::rectangle(cpu_result, rect, cv::Scalar(0, 255, 0), 2);
            cv::putText(cpu_result, "CPU-HOG", cv::Point(rect.x, rect.y-10), 
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 0), 1);
        }
        cv::imwrite("detection_cpu_result.jpg", cpu_result);
        
        // GPU版本 - 模拟DNN检测（如果有预训练模型）
        if (gpu_available) {
            timer.reset();
            
            // 模拟GPU预处理
            cv::cuda::GpuMat gpu_image, gpu_resized;
            gpu_image.upload(image);
            cv::cuda::resize(gpu_image, gpu_resized, cv::Size(416, 416));
            
            cv::Mat resized_cpu;
            gpu_resized.download(resized_cpu);
            
            double gpu_time = timer.elapsed();
            
            std::cout << "GPU预处理:" << std::endl;
            std::cout << "  处理时间: " << gpu_time << " ms" << std::endl;
            std::cout << "  加速比: " << (cpu_time / gpu_time) << "x" << std::endl;
        }
    }
    
    // 图像分割演示
    void demonstrateSegmentation(const cv::Mat& image) {
        std::cout << "\n=== 图像分割演示 ===" << std::endl;
        Timer timer;
        
        // CPU版本 - 分水岭分割
        timer.reset();
        cv::Mat gray, binary, markers;
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
        cv::threshold(gray, binary, 0, 255, cv::THRESH_BINARY_INV + cv::THRESH_OTSU);
        
        // 距离变换
        cv::Mat dist_transform;
        cv::distanceTransform(binary, dist_transform, cv::DIST_L2, 5);
        
        // 寻找种子点
        cv::Mat sure_fg;
        cv::threshold(dist_transform, sure_fg, 0.7 * 255, 255, 0);
        sure_fg.convertTo(sure_fg, CV_8U);
        
        // 寻找轮廓作为标记
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(sure_fg, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
        
        markers = cv::Mat::zeros(gray.size(), CV_32S);
        for (size_t i = 0; i < contours.size(); i++) {
            cv::drawContours(markers, contours, static_cast<int>(i), cv::Scalar(static_cast<int>(i+1)), -1);
        }
        
        cv::watershed(image, markers);
        double cpu_time = timer.elapsed();
        
        // 创建分割结果图像
        cv::Mat segmentation_result = cv::Mat::zeros(markers.size(), CV_8UC3);
        for (int i = 0; i < markers.rows; i++) {
            for (int j = 0; j < markers.cols; j++) {
                int index = markers.at<int>(i, j);
                if (index > 0 && index <= static_cast<int>(contours.size())) {
                    segmentation_result.at<cv::Vec3b>(i, j) = cv::Vec3b(
                        (index * 50) % 255, (index * 100) % 255, (index * 150) % 255);
                }
            }
        }
        
        std::cout << "CPU分水岭分割:" << std::endl;
        std::cout << "  分割时间: " << cpu_time << " ms" << std::endl;
        std::cout << "  分割区域: " << contours.size() << std::endl;
        
        cv::imwrite("segmentation_cpu_result.jpg", segmentation_result);
        
        // GPU版本 - 加速预处理
        if (gpu_available) {
            timer.reset();
            cv::cuda::GpuMat gpu_image, gpu_gray, gpu_blurred;
            gpu_image.upload(image);
            cv::cuda::cvtColor(gpu_image, gpu_gray, cv::COLOR_BGR2GRAY);
            cv::cuda::bilateralFilter(gpu_gray, gpu_blurred, 15, 50, 50);
            
            cv::Mat blurred_result;
            gpu_blurred.download(blurred_result);
            double gpu_time = timer.elapsed();
            
            std::cout << "GPU预处理加速:" << std::endl;
            std::cout << "  处理时间: " << gpu_time << " ms" << std::endl;
            std::cout << "  预处理加速比: " << (cpu_time * 0.3 / gpu_time) << "x" << std::endl;
        }
    }
    
    // 特征检测演示
    void demonstrateFeatureDetection(const cv::Mat& image) {
        std::cout << "\n=== 特征检测演示 ===" << std::endl;
        Timer timer;
        
        // CPU版本 - FAST特征检测
        timer.reset();
        std::vector<cv::KeyPoint> keypoints;
        cv::FAST(image, keypoints, 50, true);
        double cpu_time = timer.elapsed();
        
        std::cout << "CPU FAST特征检测:" << std::endl;
        std::cout << "  检测时间: " << cpu_time << " ms" << std::endl;
        std::cout << "  特征点数: " << keypoints.size() << std::endl;
        
        // 绘制特征点
        cv::Mat feature_result;
        cv::drawKeypoints(image, keypoints, feature_result, cv::Scalar(0, 255, 0));
        cv::imwrite("features_cpu_result.jpg", feature_result);
        
        // GPU版本 - 加速预处理
        if (gpu_available) {
            timer.reset();
            cv::cuda::GpuMat gpu_image, gpu_gray;
            gpu_image.upload(image);
            cv::cuda::cvtColor(gpu_image, gpu_gray, cv::COLOR_BGR2GRAY);
            
            cv::Mat gray_result;
            gpu_gray.download(gray_result);
            
            // CPU特征检测（GPU上的特征检测器较少）
            std::vector<cv::KeyPoint> gpu_keypoints;
            cv::FAST(gray_result, gpu_keypoints, 50, true);
            double gpu_time = timer.elapsed();
            
            std::cout << "GPU+CPU混合处理:" << std::endl;
            std::cout << "  总时间: " << gpu_time << " ms" << std::endl;
            std::cout << "  特征点数: " << gpu_keypoints.size() << std::endl;
        }
    }
    
    // 目标跟踪演示
    void demonstrateTracking(const std::vector<cv::Mat>& frames) {
        std::cout << "\n=== 目标跟踪演示 ===" << std::endl;
        
        if (frames.empty()) {
            std::cout << "没有足够的帧进行跟踪演示" << std::endl;
            return;
        }
        
        Timer timer;
        
        // 在第一帧中选择跟踪目标（模拟）
        cv::Rect bbox(100, 100, 200, 200);  // 假设的跟踪框

        // CPU版本 - CSRT跟踪器
        timer.reset();
        cv::Ptr<cv::Tracker> tracker = cv::TrackerCSRT::create();
        tracker->init(frames[0], bbox);

        std::vector<cv::Rect> cpu_tracks;
        cpu_tracks.push_back(bbox);

        for (size_t i = 1; i < frames.size() && i < 10; i++) {
            cv::Rect current_bbox;
            bool success = tracker->update(frames[i], current_bbox);
            if (success) {
                cpu_tracks.push_back(current_bbox);
            }
        }
        double cpu_time = timer.elapsed();
        
        std::cout << "CPU CSRT跟踪:" << std::endl;
        std::cout << "  跟踪时间: " << cpu_time << " ms" << std::endl;
        size_t total_frames = (frames.size() < 10) ? frames.size() : 10;
        std::cout << "  成功帧数: " << cpu_tracks.size() << "/" << total_frames << std::endl;
        
        // 保存跟踪结果
        if (!frames.empty() && !cpu_tracks.empty()) {
            cv::Mat tracking_result = frames.back().clone();
            cv::rectangle(tracking_result, cpu_tracks.back(), cv::Scalar(0, 255, 0), 2);
            cv::putText(tracking_result, "CPU-CSRT", 
                       cv::Point(cpu_tracks.back().x, cpu_tracks.back().y-10),
                       cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
            cv::imwrite("tracking_cpu_result.jpg", tracking_result);
        }
        
        // GPU版本 - 加速预处理
        if (gpu_available) {
            timer.reset();
            cv::cuda::GpuMat gpu_frame, gpu_roi;
            
            for (size_t i = 0; i < frames.size() && i < 10; i++) {
                gpu_frame.upload(frames[i]);
                // GPU预处理：调整大小、增强对比度等
                cv::cuda::resize(gpu_frame, gpu_roi, cv::Size(640, 480));
            }
            
            double gpu_time = timer.elapsed();
            
            std::cout << "GPU预处理加速:" << std::endl;
            std::cout << "  预处理时间: " << gpu_time << " ms" << std::endl;
            std::cout << "  预处理加速比: " << (cpu_time * 0.2 / gpu_time) << "x" << std::endl;
        }
    }
};

int main() {
    SetConsoleOutputCP(65001);
    
    std::cout << "=== 计算机视觉核心任务演示 ===" << std::endl;
    std::cout << "OpenCV 版本: " << CV_VERSION << std::endl;
    
    CVTasksDemo demo;
    
    // 创建测试图像
    std::cout << "\n准备测试数据..." << std::endl;
    
    // 创建包含多个对象的测试图像
    cv::Mat test_image = cv::Mat::zeros(600, 800, CV_8UC3);
    
    // 绘制一些矩形作为"对象"
    cv::rectangle(test_image, cv::Rect(100, 100, 120, 200), cv::Scalar(255, 100, 100), -1);
    cv::rectangle(test_image, cv::Rect(300, 150, 100, 180), cv::Scalar(100, 255, 100), -1);
    cv::rectangle(test_image, cv::Rect(500, 200, 80, 150), cv::Scalar(100, 100, 255), -1);
    
    // 添加一些噪声和纹理
    cv::Mat noise = cv::Mat::zeros(test_image.size(), test_image.type());
    cv::randu(noise, cv::Scalar(0, 0, 0), cv::Scalar(50, 50, 50));
    cv::add(test_image, noise, test_image);
    
    // 添加一些圆形
    cv::circle(test_image, cv::Point(200, 300), 50, cv::Scalar(255, 255, 100), -1);
    cv::circle(test_image, cv::Point(600, 100), 40, cv::Scalar(255, 100, 255), -1);
    
    cv::imwrite("test_input.jpg", test_image);
    
    // 创建视频帧序列
    std::vector<cv::Mat> frames;
    for (int i = 0; i < 15; i++) {
        cv::Mat frame = test_image.clone();
        // 模拟运动：移动一个矩形
        cv::rectangle(frame, cv::Rect(100 + i*10, 100 + i*5, 120, 200), cv::Scalar(255, 255, 255), 2);
        frames.push_back(frame);
    }
    
    // 执行各种任务演示
    demo.demonstrateObjectDetection(test_image);
    demo.demonstrateSegmentation(test_image);
    demo.demonstrateFeatureDetection(test_image);
    demo.demonstrateTracking(frames);
    
    std::cout << "\n=== 演示完成 ===" << std::endl;
    std::cout << "\n生成的结果文件:" << std::endl;
    std::cout << "- test_input.jpg (测试输入图像)" << std::endl;
    std::cout << "- detection_cpu_result.jpg (目标检测结果)" << std::endl;
    std::cout << "- segmentation_cpu_result.jpg (图像分割结果)" << std::endl;
    std::cout << "- features_cpu_result.jpg (特征检测结果)" << std::endl;
    std::cout << "- tracking_cpu_result.jpg (目标跟踪结果)" << std::endl;
    
    std::cout << "\n💡 关键建议:" << std::endl;
    std::cout << "1. 深度学习模型推理强烈建议使用GPU" << std::endl;
    std::cout << "2. 传统算法在CPU上通常更高效" << std::endl;
    std::cout << "3. 混合架构可以发挥两者优势" << std::endl;
    std::cout << "4. 根据具体应用场景选择最优方案" << std::endl;
    
    return 0;
}
