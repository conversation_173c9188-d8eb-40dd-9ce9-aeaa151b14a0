@echo off
chcp 65001 >nul
echo ========================================
echo    OpenCV CUDA Demo 快速开始
echo ========================================
echo.

echo [1/4] 检查 CUDA 支持...
xmake build check_cuda_support
if errorlevel 1 (
    echo ❌ 构建失败，请检查环境配置
    pause
    exit /b 1
)

echo.
echo [2/4] 运行 CUDA 支持检测...
xmake run check_cuda_support
if errorlevel 1 (
    echo ❌ CUDA 检测失败，请检查 GPU 驱动和 OpenCV 配置
    pause
    exit /b 1
)

echo.
echo [3/4] 创建测试图像...
xmake build create_sample_image
xmake run create_sample_image

echo.
echo [4/6] 运行简单演示...
xmake build simple_cuda_demo
xmake run simple_cuda_demo

echo.
echo [5/6] 运行计算机视觉任务演示...
xmake build cv_tasks_demo
xmake run cv_tasks_demo

echo.
echo [6/6] 运行深度学习推理演示...
xmake build dnn_inference_demo
xmake run dnn_inference_demo

echo.
echo ========================================
echo    演示完成！
echo ========================================
echo.
echo 生成的文件：
echo - sample.jpg (示例图像)
echo - cpu_*.jpg (CPU 处理结果)
echo - gpu_*.jpg (GPU 处理结果)
echo - test_input.jpg (CV任务测试图像)
echo - detection_cpu_result.jpg (目标检测结果)
echo - segmentation_cpu_result.jpg (图像分割结果)
echo - features_cpu_result.jpg (特征检测结果)
echo - tracking_cpu_result.jpg (目标跟踪结果)
echo - dnn_test_input.jpg (DNN测试输入)
echo - pose_estimation_result.jpg (姿态估计结果)
echo.
echo 要运行高级演示，请执行：
echo   xmake build intensive_cuda_demo
echo   xmake run intensive_cuda_demo
echo   xmake build real_world_benchmark
echo   xmake run real_world_benchmark
echo.
pause
