#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/cudaimgproc.hpp>
#include <iostream>
#include <chrono>
#include <windows.h>

class Timer {
public:
    Timer() : start_time(std::chrono::high_resolution_clock::now()) {}
    
    double elapsed() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        return duration.count() / 1000.0; // 返回毫秒
    }
    
    void reset() {
        start_time = std::chrono::high_resolution_clock::now();
    }

private:
    std::chrono::high_resolution_clock::time_point start_time;
};

void cpuImageProcessing(const cv::Mat& input, const std::string& prefix) {
    std::cout << "\n=== CPU 图像处理 ===" << std::endl;
    Timer timer;
    
    // 1. 转换为灰度图
    timer.reset();
    cv::Mat gray;
    cv::cvtColor(input, gray, cv::COLOR_BGR2GRAY);
    std::cout << "CPU 灰度转换: " << timer.elapsed() << " ms" << std::endl;
    
    // 2. 高斯模糊
    timer.reset();
    cv::Mat blurred;
    cv::GaussianBlur(gray, blurred, cv::Size(15, 15), 5.0);
    std::cout << "CPU 高斯模糊: " << timer.elapsed() << " ms" << std::endl;
    
    // 3. 边缘检测
    timer.reset();
    cv::Mat edges;
    cv::Canny(blurred, edges, 50, 150);
    std::cout << "CPU 边缘检测: " << timer.elapsed() << " ms" << std::endl;
    
    // 4. 形态学操作
    timer.reset();
    cv::Mat morphed;
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(5, 5));
    cv::morphologyEx(edges, morphed, cv::MORPH_CLOSE, kernel);
    std::cout << "CPU 形态学操作: " << timer.elapsed() << " ms" << std::endl;
    
    // 保存结果
    cv::imwrite(prefix + "_cpu_gray.jpg", gray);
    cv::imwrite(prefix + "_cpu_blurred.jpg", blurred);
    cv::imwrite(prefix + "_cpu_edges.jpg", edges);
    cv::imwrite(prefix + "_cpu_morphed.jpg", morphed);
}

void gpuImageProcessing(const cv::Mat& input, const std::string& prefix) {
    std::cout << "\n=== GPU 图像处理 ===" << std::endl;
    Timer timer;
    
    // 上传到GPU
    timer.reset();
    cv::cuda::GpuMat gpu_input;
    gpu_input.upload(input);
    std::cout << "GPU 数据上传: " << timer.elapsed() << " ms" << std::endl;
    
    // 1. 转换为灰度图
    timer.reset();
    cv::cuda::GpuMat gpu_gray;
    cv::cuda::cvtColor(gpu_input, gpu_gray, cv::COLOR_BGR2GRAY);
    std::cout << "GPU 灰度转换: " << timer.elapsed() << " ms" << std::endl;
    
    // 2. 高斯模糊
    timer.reset();
    cv::cuda::GpuMat gpu_blurred;
    cv::Ptr<cv::cuda::Filter> gaussian_filter = cv::cuda::createGaussianFilter(
        gpu_gray.type(), gpu_gray.type(), cv::Size(15, 15), 5.0);
    gaussian_filter->apply(gpu_gray, gpu_blurred);
    std::cout << "GPU 高斯模糊: " << timer.elapsed() << " ms" << std::endl;
    
    // 3. 边缘检测
    timer.reset();
    cv::cuda::GpuMat gpu_edges;
    cv::Ptr<cv::cuda::CannyEdgeDetector> canny = cv::cuda::createCannyEdgeDetector(50, 150);
    canny->detect(gpu_blurred, gpu_edges);
    std::cout << "GPU 边缘检测: " << timer.elapsed() << " ms" << std::endl;
    
    // 4. 形态学操作
    timer.reset();
    cv::cuda::GpuMat gpu_morphed;
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(5, 5));
    cv::Ptr<cv::cuda::Filter> morph_filter = cv::cuda::createMorphologyFilter(
        cv::MORPH_CLOSE, gpu_edges.type(), kernel);
    morph_filter->apply(gpu_edges, gpu_morphed);
    std::cout << "GPU 形态学操作: " << timer.elapsed() << " ms" << std::endl;
    
    // 下载结果
    timer.reset();
    cv::Mat gray, blurred, edges, morphed;
    gpu_gray.download(gray);
    gpu_blurred.download(blurred);
    gpu_edges.download(edges);
    gpu_morphed.download(morphed);
    std::cout << "GPU 数据下载: " << timer.elapsed() << " ms" << std::endl;
    
    // 保存结果
    cv::imwrite(prefix + "_gpu_gray.jpg", gray);
    cv::imwrite(prefix + "_gpu_blurred.jpg", blurred);
    cv::imwrite(prefix + "_gpu_edges.jpg", edges);
    cv::imwrite(prefix + "_gpu_morphed.jpg", morphed);
}

int main() {
    // 设置控制台输出为 UTF-8
    SetConsoleOutputCP(65001);
    
    std::cout << "=== OpenCV CUDA 图像处理演示 ===" << std::endl;
    std::cout << "OpenCV 版本: " << CV_VERSION << std::endl;
    
    // 检查CUDA支持
    int cuda_devices = cv::cuda::getCudaEnabledDeviceCount();
    if (cuda_devices == 0) {
        std::cerr << "错误: 未检测到CUDA设备!" << std::endl;
        return -1;
    }
    
    cv::cuda::DeviceInfo deviceInfo;
    std::cout << "使用GPU: " << deviceInfo.name() << std::endl;
    std::cout << "计算能力: " << deviceInfo.majorVersion() << "." << deviceInfo.minorVersion() << std::endl;
    
    // 创建测试图像 (如果没有输入图像)
    cv::Mat test_image;
    if (!cv::imread("sample.jpg").empty()) {
        test_image = cv::imread("sample.jpg");
        std::cout << "使用现有图像: sample.jpg" << std::endl;
    } else {
        // 创建一个大的测试图像
        test_image = cv::Mat::zeros(2000, 2000, CV_8UC3);
        
        // 绘制一些图案
        for (int i = 0; i < 50; i++) {
            cv::Point center(rand() % 2000, rand() % 2000);
            cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
            cv::circle(test_image, center, rand() % 100 + 20, color, -1);
        }
        
        for (int i = 0; i < 30; i++) {
            cv::Point pt1(rand() % 2000, rand() % 2000);
            cv::Point pt2(rand() % 2000, rand() % 2000);
            cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
            cv::rectangle(test_image, pt1, pt2, color, -1);
        }
        
        cv::imwrite("test_input.jpg", test_image);
        std::cout << "创建测试图像: test_input.jpg (2000x2000)" << std::endl;
    }
    
    std::cout << "图像尺寸: " << test_image.cols << "x" << test_image.rows << std::endl;
    
    // 执行CPU处理
    Timer total_timer;
    cpuImageProcessing(test_image, "demo");
    double cpu_time = total_timer.elapsed();
    
    // 执行GPU处理
    total_timer.reset();
    gpuImageProcessing(test_image, "demo");
    double gpu_time = total_timer.elapsed();
    
    // 性能对比
    std::cout << "\n=== 性能对比 ===" << std::endl;
    std::cout << "CPU 总时间: " << cpu_time << " ms" << std::endl;
    std::cout << "GPU 总时间: " << gpu_time << " ms" << std::endl;
    std::cout << "加速比: " << (cpu_time / gpu_time) << "x" << std::endl;
    
    std::cout << "\n处理完成! 输出文件:" << std::endl;
    std::cout << "- demo_cpu_*.jpg (CPU处理结果)" << std::endl;
    std::cout << "- demo_gpu_*.jpg (GPU处理结果)" << std::endl;
    
    return 0;
}
