#include <iostream>
#include <string>
#include <fstream>
#include <opencv2/opencv.hpp>
#include <windows.h>
// 日志文件流
std::ofstream logFile;

// 日志函数
void log(const std::string& message) {
    std::cout << message << std::endl;
    if (logFile.is_open()) {
        logFile << message << std::endl;
        logFile.flush();
    }
}

int main(int argc, char** argv) {
    // 打开日志文件
    SetConsoleOutputCP(65001);  // 设置控制台输出为 UTF-8
    logFile.open("opencv_demo_log.txt", std::ios::out | std::ios::app);
    
    try {
        log("程序启动...");
        
        // 检查命令行参数
        log("参数数量: " + std::to_string(argc));
        for(int i = 0; i < argc; i++) {
            log("参数 " + std::to_string(i) + ": " + argv[i]);
        }
        
        // 显示OpenCV版本信息
        log("OpenCV版本: " + std::string(CV_VERSION));
        
        // 读取图像
        cv::Mat image;
        std::string imagePath;
        if (argc > 1) {
            imagePath = argv[1];
        } else {
            imagePath = "sample.jpg";
            log("未提供图像路径，将尝试读取默认图像: " + imagePath);
        }
        
        log("尝试读取图像: " + imagePath);
        image = cv::imread(imagePath, cv::IMREAD_COLOR);
        
        if (image.empty()) {
            log("错误: 无法读取图像: " + imagePath);
            
            // 如果无法读取图像，创建一个简单的测试图像
            log("创建测试图像...");
            image = cv::Mat(480, 640, CV_8UC3, cv::Scalar(255, 255, 255));
            
            // 在图像上绘制一些形状
            cv::circle(image, cv::Point(320, 240), 100, cv::Scalar(0, 0, 255), 5);
            cv::rectangle(image, cv::Rect(200, 150, 240, 180), cv::Scalar(0, 255, 0), 3);
            cv::line(image, cv::Point(100, 100), cv::Point(540, 380), cv::Scalar(255, 0, 0), 3);
            cv::putText(image, "OpenCV Demo", cv::Point(150, 400), cv::FONT_HERSHEY_SIMPLEX, 1.5, cv::Scalar(0, 0, 0), 2);
        }
        
        log("成功获取图像，尺寸: " + std::to_string(image.cols) + "x" + std::to_string(image.rows));
        
        // 保存原始图像
        bool saveSuccess = cv::imwrite("result_original.jpg", image);
        log("原始图像保存" + std::string(saveSuccess ? "成功" : "失败") + ": result_original.jpg");
        
        // 创建处理后的图像
        cv::Mat grayImage, blurredImage, edgesImage, hsvImage;
        std::vector<cv::Mat> processedImages;
        std::vector<std::string> windowNames;
        
        // 1. 灰度图像
        log("转换为灰度图像...");
        cv::cvtColor(image, grayImage, cv::COLOR_BGR2GRAY);
        cv::imwrite("result_gray.jpg", grayImage);
        processedImages.push_back(grayImage);
        windowNames.push_back("Gray Image");
        
        // 2. 高斯模糊
        log("应用高斯模糊...");
        cv::GaussianBlur(image, blurredImage, cv::Size(15, 15), 0);
        cv::imwrite("result_blurred.jpg", blurredImage);
        processedImages.push_back(blurredImage);
        windowNames.push_back("Blurred Image");
        
        // 3. Canny边缘检测
        log("应用Canny边缘检测...");
        cv::Canny(grayImage, edgesImage, 50, 150);
        // 转换为3通道以便显示
        cv::cvtColor(edgesImage, edgesImage, cv::COLOR_GRAY2BGR);
        cv::imwrite("result_edges.jpg", edgesImage);
        processedImages.push_back(edgesImage);
        windowNames.push_back("Edge Detection");
        
        // 4. HSV颜色空间
        log("转换为HSV颜色空间...");
        cv::cvtColor(image, hsvImage, cv::COLOR_BGR2HSV);
        cv::imwrite("result_hsv.jpg", hsvImage);
        processedImages.push_back(hsvImage);
        windowNames.push_back("HSV Image");
        
        // 5. 创建拼接图像
        log("创建拼接图像...");
        int rows = 2, cols = 3;
        cv::Mat montage = cv::Mat::zeros(image.rows * rows, image.cols * cols, image.type());
        
        // 添加原始图像
        image.copyTo(montage(cv::Rect(0, 0, image.cols, image.rows)));
        cv::putText(montage, "Original", cv::Point(20, 30), cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 255, 0), 2);
        
        // 添加处理后的图像
        for (size_t i = 0; i < processedImages.size(); i++) {
            int row = (i + 1) / cols;
            int col = (i + 1) % cols;
            cv::Mat dest = montage(cv::Rect(col * image.cols, row * image.rows, image.cols, image.rows));
            
            if (processedImages[i].channels() == 1) {
                cv::cvtColor(processedImages[i], dest, cv::COLOR_GRAY2BGR);
            } else {
                processedImages[i].copyTo(dest);
            }
            
            cv::putText(montage, windowNames[i], cv::Point(col * image.cols + 20, row * image.rows + 30), 
                        cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(0, 255, 0), 2);
        }
        
        // 保存拼接图像
        cv::imwrite("result_montage.jpg", montage);
        log("拼接图像已保存为: result_montage.jpg");
        
        // 显示图像
        log("正在创建窗口...");
        try {
            cv::namedWindow("OpenCV Demo - Results", cv::WINDOW_NORMAL);
            cv::imshow("OpenCV Demo - Results", montage);
            
            log("等待按键...");
            cv::waitKey(0);
            
            log("正在清理窗口...");
            cv::destroyAllWindows();
        } catch (const cv::Exception& e) {
            log("窗口或显示错误: " + std::string(e.what()));
        }
        
    } catch (const cv::Exception& e) {
        log("OpenCV错误: " + std::string(e.what()));
        system("pause");
        return -1;
    } catch (const std::exception& e) {
        log("标准错误: " + std::string(e.what()));
        system("pause");
        return -1;
    } catch (...) {
        log("未知错误!");
        system("pause");
        return -1;
    }
    
    log("程序正常结束");
    
    // 关闭日志文件
    if (logFile.is_open()) {
        logFile.close();
    }
    
    system("pause");
    return 0;
}
