#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/cudaimgproc.hpp>
#include <opencv2/cudaarithm.hpp>
#include <opencv2/cudafilters.hpp>
#include <opencv2/cudawarping.hpp>
#include <iostream>
#include <chrono>
#include <windows.h>

class Timer {
public:
    Timer() : start_time(std::chrono::high_resolution_clock::now()) {}
    
    double elapsed() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        return duration.count() / 1000.0; // 返回毫秒
    }
    
    void reset() {
        start_time = std::chrono::high_resolution_clock::now();
    }

private:
    std::chrono::high_resolution_clock::time_point start_time;
};

void intensiveCpuProcessing(const cv::Mat& input) {
    std::cout << "\n=== CPU 密集计算 ===" << std::endl;
    Timer timer;
    
    cv::Mat current = input.clone();
    cv::Mat gray, temp;
    
    // 多次复杂操作
    for (int i = 0; i < 10; i++) {
        // 转换为灰度
        cv::cvtColor(current, gray, cv::COLOR_BGR2GRAY);
        
        // 多次高斯模糊
        for (int j = 0; j < 5; j++) {
            cv::GaussianBlur(gray, temp, cv::Size(15, 15), 5.0);
            gray = temp.clone();
        }
        
        // 转换回彩色
        cv::cvtColor(gray, current, cv::COLOR_GRAY2BGR);
        
        // 亮度调整
        current.convertTo(current, -1, 1.1, 10);
        
        std::cout << "CPU 迭代 " << (i+1) << "/10 完成" << std::endl;
    }
    
    double cpu_time = timer.elapsed();
    std::cout << "CPU 密集计算总时间: " << cpu_time << " ms" << std::endl;
    
    cv::imwrite("cpu_intensive_result.jpg", current);
}

void intensiveGpuProcessing(const cv::Mat& input) {
    std::cout << "\n=== GPU 密集计算 ===" << std::endl;
    Timer timer;
    
    // 上传到GPU
    cv::cuda::GpuMat gpu_current, gpu_gray, gpu_temp;
    gpu_current.upload(input);
    
    for (int i = 0; i < 10; i++) {
        // 转换为灰度
        cv::cuda::cvtColor(gpu_current, gpu_gray, cv::COLOR_BGR2GRAY);
        
        // 多次双边滤波 (比高斯模糊更复杂)
        for (int j = 0; j < 5; j++) {
            cv::cuda::bilateralFilter(gpu_gray, gpu_temp, 15, 50, 50);
            gpu_gray = gpu_temp.clone();
        }
        
        // 转换回彩色
        cv::cuda::cvtColor(gpu_gray, gpu_current, cv::COLOR_GRAY2BGR);
        
        // 亮度调整
        gpu_current.convertTo(gpu_current, -1, 1.1, 10);
        
        std::cout << "GPU 迭代 " << (i+1) << "/10 完成" << std::endl;
    }
    
    // 下载结果
    cv::Mat result;
    gpu_current.download(result);
    
    double gpu_time = timer.elapsed();
    std::cout << "GPU 密集计算总时间: " << gpu_time << " ms" << std::endl;
    
    cv::imwrite("gpu_intensive_result.jpg", result);
}

void batchProcessingDemo(const cv::Mat& input) {
    std::cout << "\n=== 批量处理演示 ===" << std::endl;
    
    // 创建多个不同尺寸的图像
    std::vector<cv::Mat> images;
    std::vector<cv::Size> sizes = {
        cv::Size(800, 600),
        cv::Size(1200, 900),
        cv::Size(1600, 1200),
        cv::Size(2000, 1500),
        cv::Size(2400, 1800)
    };
    
    for (const auto& size : sizes) {
        cv::Mat resized;
        cv::resize(input, resized, size);
        images.push_back(resized);
    }
    
    // CPU 批量处理
    Timer timer;
    std::cout << "\nCPU 批量处理..." << std::endl;
    for (size_t i = 0; i < images.size(); i++) {
        cv::Mat gray, blurred;
        cv::cvtColor(images[i], gray, cv::COLOR_BGR2GRAY);
        cv::GaussianBlur(gray, blurred, cv::Size(15, 15), 5.0);
        cv::imwrite("cpu_batch_" + std::to_string(i) + ".jpg", blurred);
    }
    double cpu_batch_time = timer.elapsed();
    std::cout << "CPU 批量处理时间: " << cpu_batch_time << " ms" << std::endl;
    
    // GPU 批量处理
    timer.reset();
    std::cout << "\nGPU 批量处理..." << std::endl;
    for (size_t i = 0; i < images.size(); i++) {
        cv::cuda::GpuMat gpu_img, gpu_gray, gpu_blurred;
        gpu_img.upload(images[i]);
        cv::cuda::cvtColor(gpu_img, gpu_gray, cv::COLOR_BGR2GRAY);
        cv::cuda::bilateralFilter(gpu_gray, gpu_blurred, 15, 50, 50);
        
        cv::Mat result;
        gpu_blurred.download(result);
        cv::imwrite("gpu_batch_" + std::to_string(i) + ".jpg", result);
    }
    double gpu_batch_time = timer.elapsed();
    std::cout << "GPU 批量处理时间: " << gpu_batch_time << " ms" << std::endl;
    
    std::cout << "\n批量处理对比:" << std::endl;
    std::cout << "CPU: " << cpu_batch_time << " ms" << std::endl;
    std::cout << "GPU: " << gpu_batch_time << " ms" << std::endl;
    if (gpu_batch_time < cpu_batch_time) {
        std::cout << "✓ GPU 加速比: " << (cpu_batch_time / gpu_batch_time) << "x" << std::endl;
    } else {
        std::cout << "⚠ GPU 较慢，加速比: " << (cpu_batch_time / gpu_batch_time) << "x" << std::endl;
    }
}

int main() {
    // 设置控制台输出为 UTF-8
    SetConsoleOutputCP(65001);
    
    std::cout << "=== OpenCV CUDA 密集计算演示 ===" << std::endl;
    std::cout << "OpenCV 版本: " << CV_VERSION << std::endl;
    
    // 检查CUDA支持
    int cuda_devices = cv::cuda::getCudaEnabledDeviceCount();
    if (cuda_devices == 0) {
        std::cerr << "错误: 未检测到CUDA设备!" << std::endl;
        return -1;
    }
    
    cv::cuda::DeviceInfo deviceInfo;
    std::cout << "使用GPU: " << deviceInfo.name() << std::endl;
    std::cout << "显存: " << deviceInfo.totalGlobalMem() / (1024*1024) << " MB" << std::endl;
    
    // 使用现有图像
    cv::Mat test_image;
    if (!cv::imread("large_test.jpg").empty()) {
        test_image = cv::imread("large_test.jpg");
        std::cout << "使用大尺寸图像: large_test.jpg" << std::endl;
    } else if (!cv::imread("sample.jpg").empty()) {
        test_image = cv::imread("sample.jpg");
        std::cout << "使用现有图像: sample.jpg" << std::endl;
    } else {
        std::cerr << "错误: 找不到测试图像!" << std::endl;
        return -1;
    }
    
    std::cout << "图像尺寸: " << test_image.cols << "x" << test_image.rows << std::endl;
    
    // 预热GPU (第一次调用通常较慢)
    std::cout << "\n预热GPU..." << std::endl;
    cv::cuda::GpuMat gpu_warmup;
    gpu_warmup.upload(test_image);
    cv::cuda::GpuMat gpu_gray_warmup;
    cv::cuda::cvtColor(gpu_warmup, gpu_gray_warmup, cv::COLOR_BGR2GRAY);
    cv::Mat warmup_result;
    gpu_gray_warmup.download(warmup_result);
    std::cout << "GPU 预热完成" << std::endl;
    
    // 执行密集计算对比
    Timer total_timer;
    intensiveCpuProcessing(test_image);
    double cpu_total = total_timer.elapsed();
    
    total_timer.reset();
    intensiveGpuProcessing(test_image);
    double gpu_total = total_timer.elapsed();
    
    std::cout << "\n=== 密集计算对比结果 ===" << std::endl;
    std::cout << "CPU 总时间: " << cpu_total << " ms" << std::endl;
    std::cout << "GPU 总时间: " << gpu_total << " ms" << std::endl;
    if (gpu_total < cpu_total) {
        std::cout << "✓ GPU 加速比: " << (cpu_total / gpu_total) << "x" << std::endl;
    } else {
        std::cout << "⚠ GPU 较慢，比率: " << (cpu_total / gpu_total) << "x" << std::endl;
    }
    
    // 执行批量处理演示
    batchProcessingDemo(test_image);
    
    std::cout << "\n演示完成! 查看生成的结果文件:" << std::endl;
    std::cout << "- cpu_intensive_result.jpg" << std::endl;
    std::cout << "- gpu_intensive_result.jpg" << std::endl;
    std::cout << "- cpu_batch_*.jpg" << std::endl;
    std::cout << "- gpu_batch_*.jpg" << std::endl;
    
    return 0;
}
