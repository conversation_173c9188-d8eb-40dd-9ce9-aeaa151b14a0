-- opencv_cuda_demo_xmake.lua

-- 设置项目的最小XMake版本要求
set_xmakever("2.7.2")

-- 设置项目名称
set_project("OpenCV_CUDA_Demo")

-- 设置项目版本
set_version("1.0.0")

-- 设置构建模式
add_rules("mode.debug", "mode.release")

-- 设置语言标准
set_languages("c++17")

-- 定义opencv库路径 (CUDA版本)
local opencv_dir = "E:/opencv_cuda/install"

-- 设置 UTF-8 编码
if is_plat("windows") then
    add_cxflags("/utf-8")
end

-- 主要目标
target("image_processor")
    -- 设置为可执行文件
    set_kind("binary")

    -- 添加源代码文件
    add_files("src/opencv_demo.cpp")

    -- 添加头文件路径
    add_includedirs(path.join(opencv_dir, "include"))

    -- 添加库文件路径
    add_linkdirs(path.join(opencv_dir, "x64/vc17/lib"))

    -- 添加库依赖 (只有release版本可用)
    add_links("opencv_world4130")
    if is_mode("debug") then
        add_defines("DEBUG")
    end

    -- Windows平台专用设置
    if is_plat("windows") then
        add_syslinks("kernel32", "user32", "gdi32")
        -- 设置运行时库为MD
        add_cxflags("/MD", {force = true})
        -- 添加 OpenCV 的 bin 路径
        add_rpathdirs(path.join(opencv_dir, "x64/vc17/bin"))
    end

    -- 自定义编译选项
    add_cxflags("-O2")

    if is_mode("debug") then
        add_cxflags("-g")
    end

    -- 后构建动作：复制必要的DLL文件到输出目录
    after_build(function (target)
        import("core.project.config")
        local targetdir = "$(buildir)/$(mode)"
        -- 只有release版本的DLL可用
        os.cp(path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll"), targetdir)
    end)

-- 示例图像生成器
target("create_sample_image")
    -- 设置为可执行文件
    set_kind("binary")

    -- 添加源代码文件
    add_files("src/create_sample_image.cpp")

    -- 添加头文件路径
    add_includedirs(path.join(opencv_dir, "include"))

    -- 添加库文件路径
    add_linkdirs(path.join(opencv_dir, "x64/vc17/lib"))

    -- 添加库依赖 (只有release版本可用)
    add_links("opencv_world4130")
    if is_mode("debug") then
        add_defines("DEBUG")
    end

    -- Windows平台专用设置
    if is_plat("windows") then
        add_syslinks("kernel32", "user32", "gdi32")
        -- 设置运行时库为MD
        add_cxflags("/MD", {force = true})
        -- 添加 OpenCV 的 bin 路径
        add_rpathdirs(path.join(opencv_dir, "x64/vc17/bin"))
    end

    -- 后构建动作：复制必要的DLL文件到输出目录
    after_build(function (target)
        import("core.project.config")
        local targetdir = "$(buildir)/$(mode)"
        -- 只有release版本的DLL可用
        os.cp(path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll"), targetdir)
    end)

-- CUDA支持检查工具
target("check_cuda_support")
    -- 设置为可执行文件
    set_kind("binary")

    -- 添加源代码文件
    add_files("src/check_cuda_support.cpp")

    -- 添加头文件路径
    add_includedirs(path.join(opencv_dir, "include"))

    -- 添加库文件路径
    add_linkdirs(path.join(opencv_dir, "x64/vc17/lib"))

    -- 添加库依赖 (只有release版本可用)
    add_links("opencv_world4130")
    if is_mode("debug") then
        add_defines("DEBUG")
    end

    -- Windows平台专用设置
    if is_plat("windows") then
        add_syslinks("kernel32", "user32", "gdi32")
        -- 设置运行时库为MD
        add_cxflags("/MD", {force = true})
        -- 添加 OpenCV 的 bin 路径
        add_rpathdirs(path.join(opencv_dir, "x64/vc17/bin"))
    end

    -- 后构建动作：复制必要的DLL文件到输出目录
    after_build(function (target)
        import("core.project.config")
        local targetdir = "$(buildir)/$(mode)"
        -- 只有release版本的DLL可用
        os.cp(path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll"), targetdir)
    end)

-- CUDA图像处理演示
target("cuda_demo")
    -- 设置为可执行文件
    set_kind("binary")

    -- 添加源代码文件
    add_files("src/cuda_demo.cpp")

    -- 添加头文件路径
    add_includedirs(path.join(opencv_dir, "include"))

    -- 添加库文件路径
    add_linkdirs(path.join(opencv_dir, "x64/vc17/lib"))

    -- 添加库依赖 (只有release版本可用)
    add_links("opencv_world4130")
    if is_mode("debug") then
        add_defines("DEBUG")
    end

    -- Windows平台专用设置
    if is_plat("windows") then
        add_syslinks("kernel32", "user32", "gdi32")
        -- 设置运行时库为MD
        add_cxflags("/MD", {force = true})
        -- 添加 OpenCV 的 bin 路径
        add_rpathdirs(path.join(opencv_dir, "x64/vc17/bin"))
    end

    -- 后构建动作：复制必要的DLL文件到输出目录
    after_build(function (target)
        import("core.project.config")
        local targetdir = "$(buildir)/$(mode)"
        -- 只有release版本的DLL可用
        os.cp(path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll"), targetdir)
    end)

-- 简化的CUDA演示程序
target("simple_cuda_demo")
    -- 设置为可执行文件
    set_kind("binary")

    -- 添加源代码文件
    add_files("src/simple_cuda_demo.cpp")

    -- 添加头文件路径
    add_includedirs(path.join(opencv_dir, "include"))

    -- 添加库文件路径
    add_linkdirs(path.join(opencv_dir, "x64/vc17/lib"))

    -- 添加库依赖 (只有release版本可用)
    add_links("opencv_world4130")
    if is_mode("debug") then
        add_defines("DEBUG")
    end

    -- Windows平台专用设置
    if is_plat("windows") then
        add_syslinks("kernel32", "user32", "gdi32")
        -- 设置运行时库为MD
        add_cxflags("/MD", {force = true})
        -- 添加 OpenCV 的 bin 路径
        add_rpathdirs(path.join(opencv_dir, "x64/vc17/bin"))
    end

    -- 后构建动作：复制必要的DLL文件到输出目录
    after_build(function (target)
        import("core.project.config")
        local targetdir = "$(buildir)/$(mode)"
        -- 只有release版本的DLL可用
        os.cp(path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll"), targetdir)
    end)

-- 创建大尺寸测试图像
target("create_large_image")
    -- 设置为可执行文件
    set_kind("binary")

    -- 添加源代码文件
    add_files("src/create_large_image.cpp")

    -- 添加头文件路径
    add_includedirs(path.join(opencv_dir, "include"))

    -- 添加库文件路径
    add_linkdirs(path.join(opencv_dir, "x64/vc17/lib"))

    -- 添加库依赖 (只有release版本可用)
    add_links("opencv_world4130")
    if is_mode("debug") then
        add_defines("DEBUG")
    end

    -- Windows平台专用设置
    if is_plat("windows") then
        add_syslinks("kernel32", "user32", "gdi32")
        -- 设置运行时库为MD
        add_cxflags("/MD", {force = true})
        -- 添加 OpenCV 的 bin 路径
        add_rpathdirs(path.join(opencv_dir, "x64/vc17/bin"))
    end

    -- 后构建动作：复制必要的DLL文件到输出目录
    after_build(function (target)
        import("core.project.config")
        local targetdir = "$(buildir)/$(mode)"
        -- 只有release版本的DLL可用
        os.cp(path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll"), targetdir)
    end)

-- 密集计算CUDA演示
target("intensive_cuda_demo")
    -- 设置为可执行文件
    set_kind("binary")

    -- 添加源代码文件
    add_files("src/intensive_cuda_demo.cpp")

    -- 添加头文件路径
    add_includedirs(path.join(opencv_dir, "include"))

    -- 添加库文件路径
    add_linkdirs(path.join(opencv_dir, "x64/vc17/lib"))

    -- 添加库依赖 (只有release版本可用)
    add_links("opencv_world4130")
    if is_mode("debug") then
        add_defines("DEBUG")
    end

    -- Windows平台专用设置
    if is_plat("windows") then
        add_syslinks("kernel32", "user32", "gdi32")
        -- 设置运行时库为MD
        add_cxflags("/MD", {force = true})
        -- 添加 OpenCV 的 bin 路径
        add_rpathdirs(path.join(opencv_dir, "x64/vc17/bin"))
    end

    -- 后构建动作：复制必要的DLL文件到输出目录
    after_build(function (target)
        import("core.project.config")
        local targetdir = "$(buildir)/$(mode)"
        -- 只有release版本的DLL可用
        os.cp(path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll"), targetdir)
    end)

-- 设置安装路径
set_installdir("install")

-- 自定义任务: 清理生成文件
task("clean_all")
    on_run(function ()
        import("core.project.project")
        os.rm("build")
        os.rm("install")
        cprint("${bright green}All build files have been removed!${clear}")
    end)
    set_menu({
        usage = "xmake clean_all",
        description = "Clean all build files including temporary files"
    })

-- 自定义任务: 复制DLL文件
task("copy_dlls")
    on_run(function ()
        import("core.project.config")

        -- 确保目标目录存在
        local targetdir = "build/windows/x64/release"
        os.mkdir(targetdir)

        -- 复制OpenCV DLL
        local opencv_dll = path.join(opencv_dir, "x64/vc17/bin/opencv_world4130.dll")
        if os.exists(opencv_dll) then
            os.cp(opencv_dll, targetdir)
            cprint("${green}Copied " .. opencv_dll .. " to " .. targetdir .. "${clear}")
        else
            cprint("${red}Warning: " .. opencv_dll .. " not found!${clear}")
        end

        cprint("${bright green}DLL files have been copied to " .. targetdir .. "${clear}")
    end)
    set_menu({
        usage = "xmake copy_dlls",
        description = "Copy required DLL files to the output directory"
    })
