#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/cudaimgproc.hpp>
#include <opencv2/cudaarithm.hpp>
#include <opencv2/cudafilters.hpp>
#include <opencv2/cudawarping.hpp>
#include <iostream>
#include <chrono>
#include <windows.h>

class Timer {
public:
    Timer() : start_time(std::chrono::high_resolution_clock::now()) {}
    
    double elapsed() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        return duration.count() / 1000.0; // 返回毫秒
    }
    
    void reset() {
        start_time = std::chrono::high_resolution_clock::now();
    }

private:
    std::chrono::high_resolution_clock::time_point start_time;
};

void demonstrateCpuVsGpu(const cv::Mat& input) {
    std::cout << "\n=== CPU vs GPU 性能对比演示 ===" << std::endl;
    std::cout << "图像尺寸: " << input.cols << "x" << input.rows << std::endl;
    
    Timer timer;
    
    // === CPU 版本 ===
    std::cout << "\n--- CPU 处理 ---" << std::endl;
    
    // CPU: 转换为灰度图
    timer.reset();
    cv::Mat cpu_gray;
    cv::cvtColor(input, cpu_gray, cv::COLOR_BGR2GRAY);
    double cpu_cvt_time = timer.elapsed();
    std::cout << "CPU 灰度转换: " << cpu_cvt_time << " ms" << std::endl;
    
    // CPU: 高斯模糊
    timer.reset();
    cv::Mat cpu_blurred;
    cv::GaussianBlur(cpu_gray, cpu_blurred, cv::Size(15, 15), 5.0);
    double cpu_blur_time = timer.elapsed();
    std::cout << "CPU 高斯模糊: " << cpu_blur_time << " ms" << std::endl;
    
    // CPU: 图像缩放
    timer.reset();
    cv::Mat cpu_resized;
    cv::resize(cpu_gray, cpu_resized, cv::Size(cpu_gray.cols/2, cpu_gray.rows/2));
    double cpu_resize_time = timer.elapsed();
    std::cout << "CPU 图像缩放: " << cpu_resize_time << " ms" << std::endl;
    
    // CPU: 算术运算 (增加亮度)
    timer.reset();
    cv::Mat cpu_brightened;
    cpu_gray.convertTo(cpu_brightened, -1, 1.2, 30);
    double cpu_arith_time = timer.elapsed();
    std::cout << "CPU 亮度调整: " << cpu_arith_time << " ms" << std::endl;
    
    double cpu_total = cpu_cvt_time + cpu_blur_time + cpu_resize_time + cpu_arith_time;
    std::cout << "CPU 总时间: " << cpu_total << " ms" << std::endl;
    
    // === GPU 版本 ===
    std::cout << "\n--- GPU 处理 ---" << std::endl;
    
    // GPU: 上传数据
    timer.reset();
    cv::cuda::GpuMat gpu_input;
    gpu_input.upload(input);
    double gpu_upload_time = timer.elapsed();
    std::cout << "GPU 数据上传: " << gpu_upload_time << " ms" << std::endl;
    
    // GPU: 转换为灰度图
    timer.reset();
    cv::cuda::GpuMat gpu_gray;
    cv::cuda::cvtColor(gpu_input, gpu_gray, cv::COLOR_BGR2GRAY);
    double gpu_cvt_time = timer.elapsed();
    std::cout << "GPU 灰度转换: " << gpu_cvt_time << " ms" << std::endl;
    
    // GPU: 高斯模糊
    timer.reset();
    cv::cuda::GpuMat gpu_blurred;
    cv::cuda::bilateralFilter(gpu_gray, gpu_blurred, 15, 50, 50);  // 使用双边滤波代替高斯模糊
    double gpu_blur_time = timer.elapsed();
    std::cout << "GPU 双边滤波: " << gpu_blur_time << " ms" << std::endl;
    
    // GPU: 图像缩放
    timer.reset();
    cv::cuda::GpuMat gpu_resized;
    cv::cuda::resize(gpu_gray, gpu_resized, cv::Size(gpu_gray.cols/2, gpu_gray.rows/2));
    double gpu_resize_time = timer.elapsed();
    std::cout << "GPU 图像缩放: " << gpu_resize_time << " ms" << std::endl;
    
    // GPU: 算术运算 (增加亮度)
    timer.reset();
    cv::cuda::GpuMat gpu_brightened;
    gpu_gray.convertTo(gpu_brightened, -1, 1.2, 30);
    double gpu_arith_time = timer.elapsed();
    std::cout << "GPU 亮度调整: " << gpu_arith_time << " ms" << std::endl;
    
    // GPU: 下载数据
    timer.reset();
    cv::Mat gpu_result_gray, gpu_result_blurred, gpu_result_resized, gpu_result_brightened;
    gpu_gray.download(gpu_result_gray);
    gpu_blurred.download(gpu_result_blurred);
    gpu_resized.download(gpu_result_resized);
    gpu_brightened.download(gpu_result_brightened);
    double gpu_download_time = timer.elapsed();
    std::cout << "GPU 数据下载: " << gpu_download_time << " ms" << std::endl;
    
    double gpu_compute_total = gpu_cvt_time + gpu_blur_time + gpu_resize_time + gpu_arith_time;
    double gpu_total = gpu_upload_time + gpu_compute_total + gpu_download_time;
    std::cout << "GPU 计算时间: " << gpu_compute_total << " ms" << std::endl;
    std::cout << "GPU 总时间: " << gpu_total << " ms" << std::endl;
    
    // === 性能对比 ===
    std::cout << "\n=== 性能对比结果 ===" << std::endl;
    std::cout << "CPU 总时间: " << cpu_total << " ms" << std::endl;
    std::cout << "GPU 总时间: " << gpu_total << " ms" << std::endl;
    std::cout << "GPU 计算时间: " << gpu_compute_total << " ms" << std::endl;
    
    if (gpu_compute_total < cpu_total) {
        std::cout << "✓ GPU 计算加速比: " << (cpu_total / gpu_compute_total) << "x" << std::endl;
    } else {
        std::cout << "⚠ GPU 计算较慢，可能因为图像太小或GPU初始化开销" << std::endl;
    }
    
    if (gpu_total < cpu_total) {
        std::cout << "✓ GPU 总体加速比: " << (cpu_total / gpu_total) << "x" << std::endl;
    } else {
        std::cout << "⚠ GPU 总体较慢，主要因为数据传输开销" << std::endl;
    }
    
    // 保存结果
    cv::imwrite("cpu_gray.jpg", cpu_gray);
    cv::imwrite("cpu_blurred.jpg", cpu_blurred);
    cv::imwrite("cpu_resized.jpg", cpu_resized);
    cv::imwrite("cpu_brightened.jpg", cpu_brightened);
    
    cv::imwrite("gpu_gray.jpg", gpu_result_gray);
    cv::imwrite("gpu_blurred.jpg", gpu_result_blurred);
    cv::imwrite("gpu_resized.jpg", gpu_result_resized);
    cv::imwrite("gpu_brightened.jpg", gpu_result_brightened);
    
    std::cout << "\n结果已保存到 cpu_*.jpg 和 gpu_*.jpg 文件" << std::endl;
}

int main() {
    // 设置控制台输出为 UTF-8
    SetConsoleOutputCP(65001);
    
    std::cout << "=== OpenCV CUDA 简单演示 ===" << std::endl;
    std::cout << "OpenCV 版本: " << CV_VERSION << std::endl;
    
    // 检查CUDA支持
    int cuda_devices = cv::cuda::getCudaEnabledDeviceCount();
    if (cuda_devices == 0) {
        std::cerr << "错误: 未检测到CUDA设备!" << std::endl;
        return -1;
    }
    
    cv::cuda::DeviceInfo deviceInfo;
    std::cout << "使用GPU: " << deviceInfo.name() << std::endl;
    
    // 使用现有图像或创建测试图像
    cv::Mat test_image;
    if (!cv::imread("large_test.jpg").empty()) {
        test_image = cv::imread("large_test.jpg");
        std::cout << "使用大尺寸图像: large_test.jpg" << std::endl;
    } else if (!cv::imread("sample.jpg").empty()) {
        test_image = cv::imread("sample.jpg");
        std::cout << "使用现有图像: sample.jpg" << std::endl;
    } else {
        // 创建一个较大的测试图像以便看到GPU加速效果
        test_image = cv::Mat::zeros(1500, 1500, CV_8UC3);
        
        // 绘制一些复杂图案
        for (int i = 0; i < 100; i++) {
            cv::Point center(rand() % 1500, rand() % 1500);
            cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
            cv::circle(test_image, center, rand() % 50 + 10, color, -1);
        }
        
        for (int i = 0; i < 50; i++) {
            cv::Point pt1(rand() % 1500, rand() % 1500);
            cv::Point pt2(rand() % 1500, rand() % 1500);
            cv::Scalar color(rand() % 256, rand() % 256, rand() % 256);
            cv::rectangle(test_image, pt1, pt2, color, -1);
        }
        
        cv::imwrite("test_input.jpg", test_image);
        std::cout << "创建测试图像: test_input.jpg (1500x1500)" << std::endl;
    }
    
    // 执行CPU vs GPU演示
    demonstrateCpuVsGpu(test_image);
    
    return 0;
}
