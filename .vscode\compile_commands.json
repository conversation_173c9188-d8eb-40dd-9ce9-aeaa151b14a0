[{"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\check_cuda_support\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\check_cuda_support\\windows\\x64\\release\\src\\check_cuda_support.cpp.obj", "src\\check_cuda_support.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\check_cuda_support.cpp"}, {"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\cv_tasks_demo\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\cv_tasks_demo\\windows\\x64\\release\\src\\cv_tasks_demo.cpp.obj", "src\\cv_tasks_demo.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\cv_tasks_demo.cpp"}, {"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\create_sample_image\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\create_sample_image\\windows\\x64\\release\\src\\create_sample_image.cpp.obj", "src\\create_sample_image.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\create_sample_image.cpp"}, {"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\dnn_inference_demo\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\dnn_inference_demo\\windows\\x64\\release\\src\\dnn_inference_demo.cpp.obj", "src\\dnn_inference_demo.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\dnn_inference_demo.cpp"}, {"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\create_large_image\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\create_large_image\\windows\\x64\\release\\src\\create_large_image.cpp.obj", "src\\create_large_image.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\create_large_image.cpp"}, {"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\real_world_benchmark\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\real_world_benchmark\\windows\\x64\\release\\src\\real_world_benchmark.cpp.obj", "src\\real_world_benchmark.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\real_world_benchmark.cpp"}, {"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\simple_cuda_demo\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\simple_cuda_demo\\windows\\x64\\release\\src\\simple_cuda_demo.cpp.obj", "src\\simple_cuda_demo.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\simple_cuda_demo.cpp"}, {"directory": "h:\\OpenCV_xmake_Demo", "arguments": ["E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/IE:\\opencv_cuda\\install\\include", "/Ibuild\\.gens\\intensive_cuda_demo\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\intensive_cuda_demo\\windows\\x64\\release\\src\\intensive_cuda_demo.cpp.obj", "src\\intensive_cuda_demo.cpp", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "-imsvc", "E:\\Microsoft Visual Studio\\vs2022\\VC\\Auxiliary\\VS\\include", "-imsvc", "E:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\um", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\shared", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\winrt", "-imsvc", "E:\\Windows Kits\\10\\\\include\\10.0.26100.0\\\\cppwinrt"], "file": "src\\intensive_cuda_demo.cpp"}]