[{"directory": "c:\\Users\\<USER>\\Desktop\\OpenCV_CUDA_Demo", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/O2", "/std:c++17", "/ID:\\opencv\\build\\include", "/Ibuild\\.gens\\create_sample_image\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/DNDEBUG", "/Fobuild\\.objs\\create_sample_image\\windows\\x64\\release\\src\\create_sample_image.cpp.obj", "src\\create_sample_image.cpp", "-imsvc", "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\include", "-imsvc", "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\um", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\shared", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\winrt", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\cppwinrt"], "file": "src\\create_sample_image.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\OpenCV_CUDA_Demo", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\bin\\HostX64\\x64\\cl.exe", "/c", "/nologo", "/std:c++17", "/ID:\\opencv\\build\\include", "/Ibuild\\.gens\\image_processor\\windows\\x64\\release\\platform\\windows\\idl", "/EHsc", "/utf-8", "/MD", "/O2", "/DNDEBUG", "/Fobuild\\.objs\\image_processor\\windows\\x64\\release\\src\\opencv_demo.cpp.obj", "src\\opencv_demo.cpp", "-imsvc", "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\include", "-imsvc", "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\um", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\shared", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\winrt", "-imsvc", "C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\cppwinrt"], "file": "src\\opencv_demo.cpp"}]