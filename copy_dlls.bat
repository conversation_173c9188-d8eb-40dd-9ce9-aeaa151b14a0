@echo off
echo ===== 复制OpenCV动态库 =====
echo.

REM 设置OpenCV路径（请根据您的实际安装路径修改）
set OPENCV_DIR=D:\opencv\build

REM 确保目标目录存在
if not exist "build\windows\x64\release" (
    echo 创建目标目录...
    mkdir "build\windows\x64\release"
)

echo 正在复制OpenCV动态库...

REM 复制Release版本的OpenCV动态库
echo 复制 %OPENCV_DIR%\x64\vc15\bin\opencv_world455.dll
copy "%OPENCV_DIR%\x64\vc15\bin\opencv_world455.dll" "build\windows\x64\release\" /Y

REM 检查是否成功
if %ERRORLEVEL% neq 0 (
    echo 错误：无法复制OpenCV动态库！
    echo 请检查OpenCV路径是否正确：%OPENCV_DIR%
    pause
    exit /b 1
)

echo.
echo 动态库复制完成！
echo 已将所需的DLL文件复制到：build\windows\x64\release\
echo.

pause
