# OpenCV CPU 演示程序

这是一个简单的OpenCV演示程序，仅使用CPU功能（不使用CUDA/GPU加速）。

## 功能

该演示程序展示了OpenCV的以下功能：

1. 图像读取和显示
2. 图像转换为灰度
3. 高斯模糊
4. Canny边缘检测
5. HSV颜色空间转换
6. 创建图像拼接

## 构建说明

### 前提条件

- 安装 [xmake](https://xmake.io/) 构建工具
- 安装 OpenCV（本项目使用的是OpenCV 4.5.5）

### 构建步骤

1. 确保 `xmake.lua` 文件中的 OpenCV 路径正确指向您的 OpenCV 安装位置
2. 运行以下命令构建项目：

```bash
xmake f
xmake
```

或者直接运行提供的批处理文件：

```bash
build_and_run.bat
```

3. 如果您没有示例图像，可以先构建并运行示例图像生成器：

```bash
xmake build create_sample_image
xmake run create_sample_image
```

## 运行程序

构建完成后，您可以通过以下命令运行演示程序：

```bash
# 使用默认的示例图像
xmake run image_processor

# 或者指定自己的图像
xmake run image_processor path/to/your/image.jpg
```

## 输出文件

程序会在当前目录生成以下输出文件：

- `opencv_demo_log.txt` - 程序运行日志
- `result_original.jpg` - 原始图像
- `result_gray.jpg` - 灰度图像
- `result_blurred.jpg` - 模糊后的图像
- `result_edges.jpg` - 边缘检测结果
- `result_hsv.jpg` - HSV颜色空间图像
- `result_montage.jpg` - 所有处理结果的拼接图像

## 注意事项

- 本演示程序仅使用OpenCV的CPU功能，不需要CUDA支持
- 如果您的OpenCV版本不是4.5.5，请相应地修改xmake.lua文件中的库名称
