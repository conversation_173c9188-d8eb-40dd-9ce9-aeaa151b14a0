# OpenCV CUDA Demo

这是一个使用 XMake 构建系统的 OpenCV CUDA 演示项目，展示了如何在 Windows 环境下配置和使用 OpenCV 的 CUDA 加速功能。

## 🚀 项目特性

- ✅ **完整的 CUDA 支持检测**：自动检测 CUDA 设备和功能
- ✅ **CPU vs GPU 性能对比**：直观展示 CUDA 加速效果
- ✅ **多种演示程序**：从基础到高级的 CUDA 应用示例
- ✅ **自动化构建**：使用 XMake 简化编译和部署
- ✅ **详细的性能分析**：实时显示处理时间和加速比

## 📁 项目结构

```
OpenCV_xmake_Demo/
├── src/
│   ├── create_sample_image.cpp      # 创建示例图像
│   ├── create_large_image.cpp       # 创建大尺寸测试图像
│   ├── check_cuda_support.cpp       # CUDA 支持检测
│   ├── simple_cuda_demo.cpp         # 简单 CPU vs GPU 对比
│   └── intensive_cuda_demo.cpp      # 密集计算演示
├── xmake.lua                        # XMake 构建配置
└── README.md                        # 项目文档
```

## 🛠️ 环境要求

### 硬件要求
- **NVIDIA GPU**：支持 CUDA 的显卡（计算能力 3.5+）
- **显存**：建议 4GB 以上

### 软件要求
- **操作系统**：Windows 10/11 x64
- **编译器**：Visual Studio 2022 (vc17)
- **构建工具**：XMake 2.7.2+
- **OpenCV**：4.13.0-dev CUDA 版本
- **CUDA Toolkit**：11.0+ (可选，如果需要重新编译 OpenCV)

## 📦 安装和配置

### 1. 安装 XMake

```bash
# 使用 PowerShell 安装
Invoke-Expression (Invoke-Webrequest 'https://xmake.io/psget.text' -UseBasicParsing).Content

# 或从官网下载：https://xmake.io/
```

### 2. 配置 OpenCV CUDA

本项目已配置为使用位于 `E:\opencv_cuda\install` 的 OpenCV CUDA 版本。

如果您的 OpenCV 安装在不同位置，请修改 `xmake.lua` 中的路径：

```lua
-- 定义opencv库路径 (CUDA版本)
local opencv_dir = "您的OpenCV路径"
```

### 3. 验证 CUDA 环境

运行 CUDA 支持检测：

```bash
xmake build check_cuda_support
xmake run check_cuda_support
```

预期输出：
```
=== OpenCV CUDA 支持检查 ===
OpenCV 版本: 4.13.0-dev
检测到的CUDA设备数量: 1
✓ CUDA 支持已启用!
设备名称: NVIDIA GeForce RTX 3090
计算能力: 8.6
全局内存: 24575 MB
```

## 🎯 使用指南

### 基础演示

1. **创建测试图像**
```bash
xmake build create_sample_image
xmake run create_sample_image
```

2. **创建大尺寸测试图像**
```bash
xmake build create_large_image
xmake run create_large_image
```

3. **简单 CPU vs GPU 对比**
```bash
xmake build simple_cuda_demo
xmake run simple_cuda_demo
```

### 高级演示

**密集计算性能对比**
```bash
xmake build intensive_cuda_demo
xmake run intensive_cuda_demo
```

这个演示会执行：
- 10 次迭代的复杂图像处理
- 批量处理不同尺寸的图像
- 详细的性能分析和对比

## 📊 性能基准

基于 NVIDIA GeForce RTX 3090 的测试结果：

### 密集计算 (4K 图像, 10 次迭代)
- **CPU 时间**: 308.1 ms
- **GPU 时间**: 210.6 ms
- **🏆 加速比**: 1.46x

### 适用场景分析

**✅ GPU 更适合：**
- 大尺寸图像处理 (2K+)
- 复杂滤波操作
- 批量重复计算
- 实时视频处理
- 深度学习推理

**⚠️ CPU 更适合：**
- 小图像快速处理
- 简单的一次性操作
- 频繁 CPU-GPU 数据交换的场景

## 🔧 故障排除

### 常见问题

1. **未检测到 CUDA 设备**
   ```
   错误: 未检测到CUDA设备!
   ```
   - 检查 NVIDIA 驱动是否最新
   - 确认 GPU 支持 CUDA
   - 验证 OpenCV 是否正确编译了 CUDA 支持

2. **编译错误**
   ```
   error: opencv_world4130.lib not found
   ```
   - 检查 `xmake.lua` 中的 OpenCV 路径
   - 确认 OpenCV CUDA 版本已正确安装

3. **DLL 缺失**
   ```
   无法启动此程序，因为计算机中丢失 opencv_world4130.dll
   ```
   - XMake 会自动复制 DLL，检查构建日志
   - 手动复制 `E:\opencv_cuda\install\x64\vc17\bin\*.dll` 到输出目录

4. **性能不如预期**
   - GPU 在小图像上可能不如 CPU 快（数据传输开销）
   - 尝试使用更大的图像或更复杂的操作
   - 确保 GPU 有足够的显存

### 调试技巧

1. **检查 CUDA 模块**
   ```bash
   xmake run check_cuda_support
   ```

2. **查看详细构建信息**
   ```bash
   xmake build -v
   ```

3. **清理重新构建**
   ```bash
   xmake clean
   xmake build
   ```

## 📈 扩展开发

### 添加新的演示程序

1. 在 `src/` 目录创建新的 `.cpp` 文件
2. 在 `xmake.lua` 中添加新的 target
3. 参考现有代码的结构和错误处理

### 支持的 CUDA 模块

当前 OpenCV 安装支持的 CUDA 模块：
- ✅ `opencv_cudaarithm` - CUDA 算术运算
- ✅ `opencv_cudafilters` - CUDA 滤波器
- ✅ `opencv_cudaimgproc` - CUDA 图像处理
- ✅ `opencv_cudawarping` - CUDA 图像变换

## 📄 许可证

本项目仅用于学习和演示目的。OpenCV 遵循 Apache 2.0 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**注意**：本项目专门针对 Windows + NVIDIA GPU 环境优化。如需在其他平台使用，可能需要调整配置。
