#include <opencv2/opencv.hpp>
#include <iostream>
#include <windows.h>

int main() {
    // 设置控制台输出为 UTF-8
    SetConsoleOutputCP(65001);

    std::cout << "=== OpenCV CUDA 支持检查 ===" << std::endl;
    std::cout << "OpenCV 版本: " << CV_VERSION << std::endl;

    // 检查CUDA设备数量
    int cuda_devices = cv::cuda::getCudaEnabledDeviceCount();
    std::cout << "检测到的CUDA设备数量: " << cuda_devices << std::endl;

    if (cuda_devices > 0) {
        std::cout << "✓ CUDA 支持已启用!" << std::endl;

        // 获取当前设备信息
        cv::cuda::DeviceInfo deviceInfo;
        std::cout << "设备名称: " << deviceInfo.name() << std::endl;
        std::cout << "计算能力: " << deviceInfo.majorVersion() << "." << deviceInfo.minorVersion() << std::endl;
        std::cout << "全局内存: " << deviceInfo.totalGlobalMem() / (1024*1024) << " MB" << std::endl;
        std::cout << "共享内存: " << deviceInfo.sharedMemPerBlock() / 1024 << " KB" << std::endl;
        std::cout << "多处理器数量: " << deviceInfo.multiProcessorCount() << std::endl;

        // 测试简单的GPU内存操作
        try {
            cv::Mat cpu_mat = cv::Mat::ones(100, 100, CV_32F);
            cv::cuda::GpuMat gpu_mat;
            gpu_mat.upload(cpu_mat);

            cv::Mat cpu_result;
            gpu_mat.download(cpu_result);

            std::cout << "✓ CUDA 内存操作测试成功!" << std::endl;
        } catch (const cv::Exception& e) {
            std::cout << "✗ CUDA 操作测试失败: " << e.what() << std::endl;
        }

    } else {
        std::cout << "✗ 未检测到CUDA设备或CUDA支持未启用" << std::endl;
    }

    // 检查可用的后端
    std::cout << "\n=== 可用的计算后端 ===" << std::endl;
    auto backends = cv::dnn::getAvailableBackends();
    for (const auto& backend_target : backends) {
        cv::dnn::Backend backend = backend_target.first;
        switch (backend) {
            case cv::dnn::DNN_BACKEND_DEFAULT:
                std::cout << "- Default Backend" << std::endl;
                break;
            case cv::dnn::DNN_BACKEND_HALIDE:
                std::cout << "- Halide Backend" << std::endl;
                break;
            case cv::dnn::DNN_BACKEND_INFERENCE_ENGINE:
                std::cout << "- Intel Inference Engine" << std::endl;
                break;
            case cv::dnn::DNN_BACKEND_OPENCV:
                std::cout << "- OpenCV Backend" << std::endl;
                break;
            case cv::dnn::DNN_BACKEND_VKCOM:
                std::cout << "- Vulkan Backend" << std::endl;
                break;
            case cv::dnn::DNN_BACKEND_CUDA:
                std::cout << "- CUDA Backend" << std::endl;
                break;
            default:
                std::cout << "- Unknown Backend" << std::endl;
                break;
        }
    }

    return 0;
}
