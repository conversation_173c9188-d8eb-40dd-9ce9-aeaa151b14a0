#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/cudaimgproc.hpp>
#include <opencv2/cudawarping.hpp>
#include <opencv2/cudaarithm.hpp>
#include <iostream>
#include <chrono>
#include <vector>
#include <windows.h>

class Timer {
public:
    Timer() : start_time(std::chrono::high_resolution_clock::now()) {}
    
    double elapsed() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        return duration.count() / 1000.0;
    }
    
    void reset() {
        start_time = std::chrono::high_resolution_clock::now();
    }

private:
    std::chrono::high_resolution_clock::time_point start_time;
};

class DNNInferenceDemo {
private:
    bool gpu_available;
    
public:
    DNNInferenceDemo() {
        gpu_available = (cv::cuda::getCudaEnabledDeviceCount() > 0);
        std::cout << "GPU可用: " << (gpu_available ? "是" : "否") << std::endl;
        
        if (gpu_available) {
            cv::cuda::DeviceInfo deviceInfo;
            std::cout << "GPU设备: " << deviceInfo.name() << std::endl;
            std::cout << "显存: " << deviceInfo.totalGlobalMem() / (1024*1024) << " MB" << std::endl;
        }
    }
    
    // 模拟目标检测推理
    void simulateObjectDetection(const std::vector<cv::Mat>& images) {
        std::cout << "\n=== 目标检测推理演示 ===" << std::endl;
        std::cout << "模拟YOLO/SSD推理过程" << std::endl;
        
        Timer timer;
        
        // CPU版本 - 模拟推理
        timer.reset();
        std::vector<std::vector<cv::Rect>> cpu_detections;
        
        for (const auto& image : images) {
            // 模拟CPU推理过程
            cv::Mat blob;
            cv::dnn::blobFromImage(image, 1/255.0, cv::Size(416, 416), cv::Scalar(0,0,0), true, false);
            
            // 模拟网络前向传播（耗时操作）
            cv::Mat processed = blob.clone();
            for (int i = 0; i < 100; i++) {
                cv::multiply(processed, 1.001, processed);
            }
            
            // 模拟后处理
            std::vector<cv::Rect> detections;
            detections.push_back(cv::Rect(50, 50, 100, 100));
            detections.push_back(cv::Rect(200, 150, 80, 120));
            cpu_detections.push_back(detections);
        }
        double cpu_time = timer.elapsed();
        
        std::cout << "CPU推理结果:" << std::endl;
        std::cout << "  总时间: " << cpu_time << " ms" << std::endl;
        std::cout << "  平均每张: " << cpu_time / images.size() << " ms" << std::endl;
        std::cout << "  FPS: " << (images.size() * 1000.0 / cpu_time) << std::endl;
        
        // GPU版本 - 模拟推理
        if (gpu_available) {
            timer.reset();
            std::vector<std::vector<cv::Rect>> gpu_detections;
            
            // 批量上传到GPU
            std::vector<cv::cuda::GpuMat> gpu_images;
            for (const auto& image : images) {
                cv::cuda::GpuMat gpu_img;
                gpu_img.upload(image);
                gpu_images.push_back(gpu_img);
            }
            
            // GPU批量预处理
            for (auto& gpu_img : gpu_images) {
                cv::cuda::GpuMat gpu_resized, gpu_normalized;
                cv::cuda::resize(gpu_img, gpu_resized, cv::Size(416, 416));
                gpu_resized.convertTo(gpu_normalized, CV_32F, 1.0/255.0);
                
                // 模拟GPU推理（实际中会使用DNN模块的GPU后端）
                cv::Mat cpu_result;
                gpu_normalized.download(cpu_result);
                
                // 模拟检测结果
                std::vector<cv::Rect> detections;
                detections.push_back(cv::Rect(45, 55, 105, 95));
                detections.push_back(cv::Rect(195, 145, 85, 125));
                gpu_detections.push_back(detections);
            }
            
            double gpu_time = timer.elapsed();
            
            std::cout << "GPU推理结果:" << std::endl;
            std::cout << "  总时间: " << gpu_time << " ms" << std::endl;
            std::cout << "  平均每张: " << gpu_time / images.size() << " ms" << std::endl;
            std::cout << "  FPS: " << (images.size() * 1000.0 / gpu_time) << std::endl;
            std::cout << "  加速比: " << (cpu_time / gpu_time) << "x" << std::endl;
        }
    }
    
    // 模拟语义分割推理
    void simulateSemanticSegmentation(const std::vector<cv::Mat>& images) {
        std::cout << "\n=== 语义分割推理演示 ===" << std::endl;
        std::cout << "模拟DeepLab/U-Net推理过程" << std::endl;
        
        Timer timer;
        
        // CPU版本
        timer.reset();
        for (const auto& image : images) {
            // 模拟分割预处理
            cv::Mat resized, normalized;
            cv::resize(image, resized, cv::Size(513, 513));
            resized.convertTo(normalized, CV_32F, 1.0/255.0);
            
            // 模拟分割推理（计算密集）
            cv::Mat segmentation = cv::Mat::zeros(513, 513, CV_8UC1);
            for (int y = 0; y < segmentation.rows; y++) {
                for (int x = 0; x < segmentation.cols; x++) {
                    // 模拟复杂计算
                    float val = std::sin(x * 0.01) * std::cos(y * 0.01);
                    segmentation.at<uchar>(y, x) = static_cast<uchar>((val + 1) * 127);
                }
            }
        }
        double cpu_time = timer.elapsed();
        
        std::cout << "CPU分割结果:" << std::endl;
        std::cout << "  总时间: " << cpu_time << " ms" << std::endl;
        std::cout << "  平均每张: " << cpu_time / images.size() << " ms" << std::endl;
        
        // GPU版本
        if (gpu_available) {
            timer.reset();
            
            for (const auto& image : images) {
                cv::cuda::GpuMat gpu_image, gpu_resized, gpu_normalized;
                gpu_image.upload(image);
                cv::cuda::resize(gpu_image, gpu_resized, cv::Size(513, 513));
                gpu_resized.convertTo(gpu_normalized, CV_32F, 1.0/255.0);
                
                // 模拟GPU并行分割计算
                cv::cuda::GpuMat gpu_segmentation;
                cv::cuda::threshold(gpu_normalized, gpu_segmentation, 0.5, 255, cv::THRESH_BINARY);
                
                cv::Mat result;
                gpu_segmentation.download(result);
            }
            
            double gpu_time = timer.elapsed();
            
            std::cout << "GPU分割结果:" << std::endl;
            std::cout << "  总时间: " << gpu_time << " ms" << std::endl;
            std::cout << "  平均每张: " << gpu_time / images.size() << " ms" << std::endl;
            std::cout << "  加速比: " << (cpu_time / gpu_time) << "x" << std::endl;
        }
    }
    
    // 模拟姿态估计推理
    void simulatePoseEstimation(const std::vector<cv::Mat>& images) {
        std::cout << "\n=== 姿态估计推理演示 ===" << std::endl;
        std::cout << "模拟OpenPose/PoseNet推理过程" << std::endl;
        
        Timer timer;
        
        // CPU版本
        timer.reset();
        std::vector<std::vector<cv::Point2f>> cpu_poses;
        
        for (const auto& image : images) {
            // 模拟姿态估计预处理
            cv::Mat resized, blob;
            cv::resize(image, resized, cv::Size(368, 368));
            cv::dnn::blobFromImage(resized, blob, 1.0/255, cv::Size(368, 368), cv::Scalar(0,0,0), false, false);
            
            // 模拟关键点检测
            std::vector<cv::Point2f> keypoints;
            for (int i = 0; i < 17; i++) {  // 17个关键点（COCO格式）
                float x = 100 + (i % 4) * 50 + (rand() % 20 - 10);
                float y = 100 + (i / 4) * 40 + (rand() % 20 - 10);
                keypoints.push_back(cv::Point2f(x, y));
            }
            cpu_poses.push_back(keypoints);
        }
        double cpu_time = timer.elapsed();
        
        std::cout << "CPU姿态估计结果:" << std::endl;
        std::cout << "  总时间: " << cpu_time << " ms" << std::endl;
        std::cout << "  平均每张: " << cpu_time / images.size() << " ms" << std::endl;
        std::cout << "  检测到的姿态: " << cpu_poses.size() << std::endl;
        
        // 保存姿态估计结果
        if (!images.empty() && !cpu_poses.empty()) {
            cv::Mat pose_result = images[0].clone();
            const auto& pose = cpu_poses[0];
            
            // 绘制关键点
            for (const auto& point : pose) {
                cv::circle(pose_result, point, 3, cv::Scalar(0, 255, 0), -1);
            }
            
            // 绘制骨架连接（简化版）
            if (pose.size() >= 17) {
                // 头部到肩膀
                cv::line(pose_result, pose[0], pose[5], cv::Scalar(255, 0, 0), 2);
                cv::line(pose_result, pose[0], pose[6], cv::Scalar(255, 0, 0), 2);
                // 肩膀到手肘
                cv::line(pose_result, pose[5], pose[7], cv::Scalar(0, 255, 0), 2);
                cv::line(pose_result, pose[6], pose[8], cv::Scalar(0, 255, 0), 2);
                // 手肘到手腕
                cv::line(pose_result, pose[7], pose[9], cv::Scalar(0, 0, 255), 2);
                cv::line(pose_result, pose[8], pose[10], cv::Scalar(0, 0, 255), 2);
            }
            
            cv::imwrite("pose_estimation_result.jpg", pose_result);
        }
        
        // GPU版本
        if (gpu_available) {
            timer.reset();
            
            for (const auto& image : images) {
                cv::cuda::GpuMat gpu_image, gpu_resized;
                gpu_image.upload(image);
                cv::cuda::resize(gpu_image, gpu_resized, cv::Size(368, 368));
                
                // 模拟GPU姿态估计
                cv::Mat cpu_resized;
                gpu_resized.download(cpu_resized);
                
                // 模拟关键点检测（实际中会使用GPU DNN推理）
                std::vector<cv::Point2f> keypoints;
                for (int i = 0; i < 17; i++) {
                    float x = 100 + (i % 4) * 50;
                    float y = 100 + (i / 4) * 40;
                    keypoints.push_back(cv::Point2f(x, y));
                }
            }
            
            double gpu_time = timer.elapsed();
            
            std::cout << "GPU姿态估计结果:" << std::endl;
            std::cout << "  总时间: " << gpu_time << " ms" << std::endl;
            std::cout << "  平均每张: " << gpu_time / images.size() << " ms" << std::endl;
            std::cout << "  加速比: " << (cpu_time / gpu_time) << "x" << std::endl;
        }
    }
    
    // 展示DNN后端选择
    void demonstrateDNNBackends() {
        std::cout << "\n=== DNN后端支持检查 ===" << std::endl;
        
        // 检查可用的后端
        auto backends = cv::dnn::getAvailableBackends();
        std::cout << "可用的DNN后端:" << std::endl;
        
        for (const auto& backend_target : backends) {
            cv::dnn::Backend backend = backend_target.first;
            cv::dnn::Target target = backend_target.second;
            
            std::string backend_name, target_name;
            
            switch (backend) {
                case cv::dnn::DNN_BACKEND_DEFAULT:
                    backend_name = "Default";
                    break;
                case cv::dnn::DNN_BACKEND_HALIDE:
                    backend_name = "Halide";
                    break;
                case cv::dnn::DNN_BACKEND_INFERENCE_ENGINE:
                    backend_name = "Intel Inference Engine";
                    break;
                case cv::dnn::DNN_BACKEND_OPENCV:
                    backend_name = "OpenCV";
                    break;
                case cv::dnn::DNN_BACKEND_VKCOM:
                    backend_name = "Vulkan";
                    break;
                case cv::dnn::DNN_BACKEND_CUDA:
                    backend_name = "CUDA";
                    break;
                default:
                    backend_name = "Unknown";
                    break;
            }
            
            switch (target) {
                case cv::dnn::DNN_TARGET_CPU:
                    target_name = "CPU";
                    break;
                case cv::dnn::DNN_TARGET_OPENCL:
                    target_name = "OpenCL";
                    break;
                case cv::dnn::DNN_TARGET_OPENCL_FP16:
                    target_name = "OpenCL FP16";
                    break;
                case cv::dnn::DNN_TARGET_CUDA:
                    target_name = "CUDA";
                    break;
                case cv::dnn::DNN_TARGET_CUDA_FP16:
                    target_name = "CUDA FP16";
                    break;
                default:
                    target_name = "Unknown";
                    break;
            }
            
            std::cout << "  - " << backend_name << " + " << target_name << std::endl;
        }
        
        std::cout << "\n💡 推荐配置:" << std::endl;
        if (gpu_available) {
            std::cout << "  目标检测: DNN_BACKEND_CUDA + DNN_TARGET_CUDA" << std::endl;
            std::cout << "  语义分割: DNN_BACKEND_CUDA + DNN_TARGET_CUDA" << std::endl;
            std::cout << "  姿态估计: DNN_BACKEND_CUDA + DNN_TARGET_CUDA" << std::endl;
        } else {
            std::cout << "  所有任务: DNN_BACKEND_OPENCV + DNN_TARGET_CPU" << std::endl;
        }
    }
};

int main() {
    SetConsoleOutputCP(65001);
    
    std::cout << "=== 深度学习推理演示 ===" << std::endl;
    std::cout << "OpenCV 版本: " << CV_VERSION << std::endl;
    
    DNNInferenceDemo demo;
    
    // 创建测试图像
    std::cout << "\n准备测试数据..." << std::endl;
    std::vector<cv::Mat> test_images;
    
    for (int i = 0; i < 5; i++) {
        cv::Mat image = cv::Mat::zeros(480, 640, CV_8UC3);
        
        // 添加一些模拟的"人体"形状
        cv::rectangle(image, cv::Rect(200 + i*20, 100, 60, 150), cv::Scalar(100, 150, 200), -1);
        cv::circle(image, cv::Point(230 + i*20, 80), 25, cv::Scalar(200, 180, 150), -1);
        
        // 添加背景纹理
        cv::Mat noise = cv::Mat::zeros(image.size(), image.type());
        cv::randu(noise, cv::Scalar(0, 0, 0), cv::Scalar(50, 50, 50));
        cv::add(image, noise, image);
        
        test_images.push_back(image);
    }
    
    cv::imwrite("dnn_test_input.jpg", test_images[0]);
    
    // 执行各种推理演示
    demo.demonstrateDNNBackends();
    demo.simulateObjectDetection(test_images);
    demo.simulateSemanticSegmentation(test_images);
    demo.simulatePoseEstimation(test_images);
    
    std::cout << "\n=== 演示完成 ===" << std::endl;
    std::cout << "\n生成的结果文件:" << std::endl;
    std::cout << "- dnn_test_input.jpg (测试输入)" << std::endl;
    std::cout << "- pose_estimation_result.jpg (姿态估计结果)" << std::endl;
    
    std::cout << "\n🚀 深度学习推理优化建议:" << std::endl;
    std::cout << "1. 使用GPU后端进行推理 (CUDA)" << std::endl;
    std::cout << "2. 批量处理提高GPU利用率" << std::endl;
    std::cout << "3. 选择合适的输入尺寸平衡精度和速度" << std::endl;
    std::cout << "4. 考虑使用FP16精度加速推理" << std::endl;
    std::cout << "5. 预处理和后处理也可以GPU加速" << std::endl;
    
    return 0;
}
