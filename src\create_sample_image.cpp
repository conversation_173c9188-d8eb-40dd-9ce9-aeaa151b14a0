#include <opencv2/opencv.hpp>
#include <iostream>
#include <windows.h>


int main() {
    // 创建一个800x600的白色背景图像
    SetConsoleOutputCP(65001);  // 设置控制台输出为 UTF-8
    cv::Mat sampleImage(600, 800, CV_8UC3, cv::Scalar(255, 255, 255));
    
    // 绘制一些形状
    cv::circle(sampleImage, cv::Point(400, 300), 150, cv::Scalar(0, 0, 255), -1);
    cv::rectangle(sampleImage, cv::Rect(100, 100, 200, 200), cv::Scalar(0, 255, 0), -1);
    cv::line(sampleImage, cv::Point(50, 50), cv::Point(750, 550), cv::Scalar(255, 0, 0), 5);
    
    // 添加文本
    cv::putText(sampleImage, "OpenCV Sample Image", cv::Point(200, 500), 
                cv::FONT_HERSHEY_SIMPLEX, 1.5, cv::Scalar(0, 0, 0), 3);
    
    // 保存图像
    bool success = cv::imwrite("sample.jpg", sampleImage);
    
    if (success) {
        std::cout << "示例图像已成功创建: sample.jpg" << std::endl;
    } else {
        std::cerr << "创建示例图像失败!" << std::endl;
        return -1;
    }
    
    return 0;
}
