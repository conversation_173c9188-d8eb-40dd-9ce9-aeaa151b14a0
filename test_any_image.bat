@echo off
echo ===== 图像处理测试工具 =====
echo.

REM 检查参数
if "%~1"=="" (
    echo 请提供图像路径作为参数。
    echo 用法: test_any_image.bat [图像路径]
    echo 例如: test_any_image.bat C:\path\to\image.jpg
    
    echo.
    echo 或者现在输入图像路径:
    set /p IMAGE_PATH=图像路径: 
) else (
    set IMAGE_PATH=%~1
)

REM 检查图像是否存在
if not exist "%IMAGE_PATH%" (
    echo 错误：找不到图像文件！
    echo 请确保文件存在：%IMAGE_PATH%
    pause
    exit /b 1
)

REM 检查可执行文件是否存在
set EXE_PATH=build\windows\x64\release\image_processor.exe
if not exist "%EXE_PATH%" (
    echo 错误：找不到可执行文件！
    echo 请确保已经构建项目：%EXE_PATH%
    pause
    exit /b 1
)

REM 确保DLL文件存在
set DLL_PATH=build\windows\x64\release\opencv_world455.dll
if not exist "%DLL_PATH%" (
    echo 警告：找不到OpenCV DLL文件！
    echo 正在尝试复制DLL文件...
    
    REM 尝试运行复制DLL的任务
    xmake copy_dlls
    
    REM 再次检查DLL是否存在
    if not exist "%DLL_PATH%" (
        echo 错误：无法复制OpenCV DLL文件！
        echo 请手动复制DLL文件或运行：xmake copy_dlls
        pause
        exit /b 1
    )
)

echo 正在处理图像：%IMAGE_PATH%
echo.

REM 运行程序
"%EXE_PATH%" "%IMAGE_PATH%"

echo.
echo 处理完成！
echo 输出文件应该已经生成在当前目录。
echo.

REM 检查是否生成了结果文件
if exist "result_montage.jpg" (
    echo 已生成拼接结果图像：result_montage.jpg
    
    REM 尝试打开结果图像
    echo 是否要打开结果图像？(Y/N)
    set /p OPEN_IMAGE=
    
    if /i "%OPEN_IMAGE%"=="Y" (
        start result_montage.jpg
    )
)

pause
